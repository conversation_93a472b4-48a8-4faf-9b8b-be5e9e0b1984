<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Modern Header --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    User Management
                </h1>
                <p class="text-gray-300 text-lg">Manage all users and their permissions</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('admin.users.create') }}"
                   class="group bg-white text-black px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-100 hover:scale-105 flex items-center space-x-2 shadow-lg">
                    <i class="fa-solid fa-plus transition-transform duration-300 group-hover:rotate-90"></i>
                    <span>Add New User</span>
                </a>
            </div>
        </div>
    </div>

    {{-- Search and Filters --}}
    <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input wire:model.live.debounce.300ms="search"
                           type="text"
                           class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-black focus:border-black transition-all duration-200"
                           placeholder="Search users by name or email...">
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="text-sm text-gray-500">
                    Showing {{ $users->count() }} of {{ $users->total() }} users
                </div>
            </div>
        </div>
    </div>

    {{-- Modern Users Grid --}}
    <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        {{-- Table Header --}}
        <div class="bg-gradient-to-r from-gray-50 to-white px-6 py-4 border-b border-gray-200">
            <div class="grid grid-cols-12 gap-4 items-center text-sm font-semibold text-gray-600 uppercase tracking-wide">
                <div class="col-span-3 cursor-pointer hover:text-gray-900 transition-colors duration-200" wire:click="sortBy('name')">
                    <div class="flex items-center space-x-2">
                        <span>Name</span>
                        @if($sortField === 'name')
                            <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} text-black"></i>
                        @else
                            <i class="fas fa-sort text-gray-400"></i>
                        @endif
                    </div>
                </div>
                <div class="col-span-3 cursor-pointer hover:text-gray-900 transition-colors duration-200" wire:click="sortBy('email')">
                    <div class="flex items-center space-x-2">
                        <span>Email</span>
                        @if($sortField === 'email')
                            <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} text-black"></i>
                        @else
                            <i class="fas fa-sort text-gray-400"></i>
                        @endif
                    </div>
                </div>
                <div class="col-span-2">Role</div>
                <div class="col-span-2 cursor-pointer hover:text-gray-900 transition-colors duration-200" wire:click="sortBy('created_at')">
                    <div class="flex items-center space-x-2">
                        <span>Joined</span>
                        @if($sortField === 'created_at')
                            <i class="fas fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} text-black"></i>
                        @else
                            <i class="fas fa-sort text-gray-400"></i>
                        @endif
                    </div>
                </div>
                <div class="col-span-2 text-center">Actions</div>
            </div>
        </div>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse ($users as $user)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $user->name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $user->email }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                @foreach($user->roles as $role)
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-200 text-gray-800">
                                        {{ ucfirst($role->name) }}
                                    </span>
                                @endforeach
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ $user->created_at->format('M d, Y') }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="{{ route('admin.users.edit', $user) }}" class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">Edit</a>
                                <button wire:click="confirmDelete({{ $user->id }})" class="ml-4 text-red-600 hover:text-red-900 dark:hover:text-red-400">Delete</button>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500 dark:text-gray-300">
                                No users found.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="mt-6">
            {{ $users->links() }}
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <x-modal.card title="Delete User" blur wire:model.defer="deleting">
        <div class="text-center">
            <p class="text-lg text-gray-600 dark:text-gray-300">Are you sure you want to delete this user?</p>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">This action cannot be undone.</p>
        </div>
 
        <x-slot name="footer">
            <div class="flex justify-end gap-x-4">
                <x-button flat label="Cancel" x-on:click="close" />
                <x-button negative label="Delete" wire:click="deleteUser" />
            </div>
        </x-slot>
    </x-modal.card>
</div>
