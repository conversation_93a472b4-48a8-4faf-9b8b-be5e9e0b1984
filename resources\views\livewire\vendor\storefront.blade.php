<div>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Vendor Header -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg mb-8">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <div class="flex items-center">
                        <img src="{{ $vendor->logo_url }}" alt="{{ $vendor->shop_name }} Logo" class="h-24 w-24 rounded-full object-cover mr-6">
                        <div>
                            <h1 class="text-3xl font-bold">{{ $vendor->shop_name }}</h1>
                            <p class="mt-2 text-gray-600 dark:text-gray-400">{{ $vendor->description }}</p>
                            @if($vendor->website)
                                <a href="{{ $vendor->website }}" target="_blank" class="text-blue-500 hover:underline mt-2 inline-block">Visit Website</a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                <!-- Categories Sidebar -->
                <aside class="lg:col-span-1">
                    <div class="p-4 bg-white dark:bg-gray-800 shadow-sm sm:rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Categories</h3>
                        <ul>
                            @foreach($categories as $category)
                                <li class="mb-2">
                                    <a href="{{ route('products.index', ['category' => $category->slug, 'vendor' => $vendor->slug]) }}" class="text-gray-700 dark:text-gray-300 hover:text-blue-500">
                                        {{ $category->name }} ({{ $category->products_count }})
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </aside>

                <!-- Products Grid -->
                <main class="lg:col-span-3">
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                        @forelse ($products as $product)
                            <x-products.card :product="$product" />
                        @empty
                            <div class="sm:col-span-2 md:col-span-3 text-center py-12">
                                <p class="text-lg text-gray-500">This vendor has no products yet.</p>
                            </div>
                        @endforelse
                    </div>

                    <!-- Pagination -->
                    <div class="mt-8">
                        {{ $products->links() }}
                    </div>
                </main>
            </div>
        </div>
    </div>
</div>
