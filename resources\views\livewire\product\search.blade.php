<div>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="mb-8">
                <x-input wire:model.live.debounce.300ms="query" placeholder="Search for products..." class="w-full" />
            </div>

            <h3 class="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-6">
                @if($query)
                    Search results for "<span class="font-bold">{{ $query }}</span>"
                @else
                    All Products
                @endif
            </h3>

            <div wire:loading.class="opacity-50" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                @forelse ($products as $product)
                    <x-products.card :product="$product" />
                @empty
                    <div class="col-span-full text-center py-12">
                        <p class="text-lg text-gray-500">No products found for your search.</p>
                    </div>
                @endforelse
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $products->links() }}
            </div>
        </div>
    </div>
</div>
