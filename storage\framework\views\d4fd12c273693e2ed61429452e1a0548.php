<div>
    <h1 class="text-3xl font-bold tracking-tight text-black"><?php echo e($product->name); ?></h1>

    <div class="mt-3">
        <h2 class="sr-only">Product information</h2>
        <p class="text-3xl tracking-tight text-black">₦<?php echo e(number_format($this->selectedVariant ? $this->selectedVariant->price : $product->price, 2)); ?></p>
        <!--[if BLOCK]><![endif]--><?php if($this->selectedVariant && $this->selectedVariant->original_price > $this->selectedVariant->price): ?>
            <p class="text-xl text-gray-500 line-through ml-2">₦<?php echo e(number_format($this->selectedVariant->original_price, 2)); ?></p>
        <?php elseif($product->is_on_sale): ?>
            <p class="text-xl text-gray-500 line-through ml-2">₦<?php echo e(number_format($product->original_price, 2)); ?></p>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <div class="mt-4">
        <div class="flex items-center">
            <div class="flex items-center">
                <!--[if BLOCK]><![endif]--><?php if($product->reviewCount() > 0): ?>
                    <!--[if BLOCK]><![endif]--><?php for($i = 1; $i <= 5; $i++): ?>
                        <svg class="h-5 w-5 flex-shrink-0 <?php echo e($i <= $product->averageRating() ? 'text-yellow-400' : 'text-gray-300'); ?>" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10.868 2.884c.321-.662 1.215-.662 1.536 0l1.681 3.462 3.82.556c.734.107 1.03.998.494 1.512l-2.764 2.693.654 3.803c.124.722-.638 1.283-1.286.944L10 13.6l-3.416 1.795c-.648.34-1.41-.222-1.286-.944l.654-3.803-2.764-2.693c-.536-.514-.24-1.405.494-1.512l3.82-.556 1.681-3.462z" clip-rule="evenodd" />
                        </svg>
                    <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                    <a href="#reviews" class="ml-3 text-sm font-medium text-gray-600 hover:text-black"><?php echo e($product->reviewCount()); ?> <?php echo e(Str::plural('review', $product->reviewCount())); ?></a>
                <?php else: ?>
                    <span class="text-sm text-gray-500">No reviews yet</span>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
            <span class="mx-4 text-gray-300">|</span>
            <span class="text-sm text-gray-500">Vendor: <a href="<?php echo e(route('vendors.storefront', $product->vendor->slug)); ?>" class="font-medium text-gray-600 hover:text-black"><?php echo e($product->vendor->shop_name); ?></a></span>
        </div>
    </div>

    <p class="mt-6 text-gray-500"><?php echo e($product->short_description); ?></p>

    <form wire:submit.prevent="addToCart" class="mt-6">
        <!--[if BLOCK]><![endif]--><?php if($variants->isNotEmpty()): ?>
            <div>
                <h3 class="text-sm font-medium text-black">Size</h3>
                <fieldset class="mt-2">
                    <legend class="sr-only">Choose a size</legend>
                    <div class="grid grid-cols-4 gap-3 sm:grid-cols-8">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $variants->unique('size_id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $variant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <label wire:click="selectSize('<?php echo e($variant->size->name); ?>')" class="<?php echo e($selectedSize === $variant->size->name ? 'ring-2 ring-black' : 'ring-1 ring-gray-300'); ?> <?php echo e($variant->stock > 0 ? 'cursor-pointer' : 'cursor-not-allowed opacity-25'); ?> group relative flex items-center justify-center rounded-md border py-3 px-3 text-sm font-medium uppercase text-black hover:bg-gray-50 focus:outline-none sm:flex-1">
                                <input type="radio" name="size-choice" value="<?php echo e($variant->size->name); ?>" class="sr-only" <?php echo e($variant->stock <= 0 ? 'disabled' : ''); ?>>
                                <span><?php echo e($variant->size->name); ?></span>
                                <span class="pointer-events-none absolute -inset-px rounded-md" aria-hidden="true"></span>
                            </label>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </fieldset>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <div class="mt-8 flex items-center gap-x-4">
            <div class="flex items-center border border-gray-300 rounded-md">
                <button type="button" wire:click="decrementQuantity" class="px-3 py-2 text-gray-500 hover:bg-gray-100 rounded-l-md">-</button>
                <input type="text" wire:model="quantity" class="w-12 text-center border-0 focus:ring-0" readonly>
                <button type="button" wire:click="incrementQuantity" class="px-3 py-2 text-gray-500 hover:bg-gray-100 rounded-r-md">+</button>
            </div>

            <button type="submit" class="flex-1 bg-black border border-transparent rounded-md py-3 px-8 flex items-center justify-center text-base font-medium text-white hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50" <?php echo e(($this->selectedVariant && $this->selectedVariant->stock < 1) || (!$this->selectedVariant && $product->stock < 1) ? 'disabled' : ''); ?>>
                <span wire:loading.remove wire:target="addToCart">
                    <?php echo e(($this->selectedVariant && $this->selectedVariant->stock < 1) || (!$this->selectedVariant && $product->stock < 1) ? 'Out of Stock' : 'Add to Cart'); ?>

                </span>
                <span wire:loading wire:target="addToCart">Adding...</span>
            </button>
        </div>
    </form>

    <div class="mt-4">
        <button wire:click="toggleWishlist" class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-black bg-white hover:bg-gray-50">
            <svg class="h-5 w-5 mr-2 <?php echo e($inWishlist ? 'text-red-500' : 'text-gray-400'); ?>" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
            </svg>
            <span wire:loading.remove wire:target="toggleWishlist">
                <?php echo e($inWishlist ? 'Remove from Wishlist' : 'Add to Wishlist'); ?>

            </span>
            <span wire:loading wire:target="toggleWishlist">Loading...</span>
        </button>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Music\brandifyng\brandifyng\resources\views/livewire/product/options.blade.php ENDPATH**/ ?>