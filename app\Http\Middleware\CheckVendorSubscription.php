<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckVendorSubscription
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        if (!Auth::check() || !Auth::user()->vendor) {
            return $next($request);
        }

        $vendor = Auth::user()->vendor;
        $orderCount = $vendor->orders()->count();

        // If vendor has 10 or fewer orders, they're still in free tier
        if ($orderCount <= 10) {
            return $next($request);
        }

        // Check if vendor has an active subscription
        $subscription = $vendor->subscription;
        
        if (!$subscription || $subscription->status !== 'active' || $subscription->ends_at < now()) {
            // Redirect to subscription page with warning
            return redirect()->route('vendor.subscription.index')
                ->with('error', 'Your free trial has ended. Please subscribe to continue receiving orders.');
        }

        return $next($request);
    }
}
