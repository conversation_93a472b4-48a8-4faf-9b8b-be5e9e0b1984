<?php

namespace App\Services;

use App\Models\Vendor;
use App\Models\VendorSubscription;
use App\Models\SubscriptionPlan;
use Carbon\Carbon;

class SubscriptionService
{
    /**
     * Check if vendor can receive new orders
     */
    public function canReceiveOrders(Vendor $vendor): bool
    {
        $orderCount = $vendor->orders()->count();
        
        // First 10 orders are free
        if ($orderCount < 10) {
            return true;
        }
        
        // Check if vendor has active subscription
        $subscription = $vendor->subscription;
        
        return $subscription && 
               $subscription->status === 'active' && 
               $subscription->ends_at > now();
    }
    
    /**
     * Get vendor's subscription status
     */
    public function getSubscriptionStatus(Vendor $vendor): array
    {
        $orderCount = $vendor->orders()->count();
        $subscription = $vendor->subscription;
        
        if ($orderCount < 10) {
            return [
                'status' => 'free_trial',
                'orders_remaining' => 10 - $orderCount,
                'message' => 'You have ' . (10 - $orderCount) . ' free orders remaining.',
                'can_receive_orders' => true
            ];
        }
        
        if (!$subscription || $subscription->status !== 'active') {
            return [
                'status' => 'subscription_required',
                'orders_remaining' => 0,
                'message' => 'Your free trial has ended. Please subscribe to continue receiving orders.',
                'can_receive_orders' => false
            ];
        }
        
        if ($subscription->ends_at < now()) {
            return [
                'status' => 'subscription_expired',
                'orders_remaining' => 0,
                'message' => 'Your subscription has expired. Please renew to continue receiving orders.',
                'can_receive_orders' => false
            ];
        }
        
        $daysRemaining = now()->diffInDays($subscription->ends_at, false);
        
        return [
            'status' => 'active',
            'orders_remaining' => 'unlimited',
            'message' => 'Your subscription is active. ' . max(0, $daysRemaining) . ' days remaining.',
            'can_receive_orders' => true,
            'days_remaining' => max(0, $daysRemaining)
        ];
    }
    
    /**
     * Create or update the standard subscription plan
     */
    public function ensureStandardPlan(): SubscriptionPlan
    {
        return SubscriptionPlan::updateOrCreate(
            ['name' => 'Standard Plan'],
            [
                'price' => 9000, // ₦9,000
                'interval' => 'monthly',
                'duration_days' => 30,
                'features' => json_encode([
                    'Unlimited orders',
                    'Priority support',
                    'Advanced analytics',
                    'Marketing tools'
                ]),
                'order_limit' => null, // Unlimited
                'status' => 'active'
            ]
        );
    }
    
    /**
     * Block vendor from receiving new orders
     */
    public function blockVendorOrders(Vendor $vendor): void
    {
        // This could be implemented by updating a status field
        // For now, we rely on the middleware and service checks
    }
    
    /**
     * Send subscription reminder notifications
     */
    public function sendSubscriptionReminders(): void
    {
        // Get vendors whose free trial is ending soon (8-9 orders)
        $vendorsNearingLimit = Vendor::whereHas('orders', function($query) {
            $query->havingRaw('COUNT(*) BETWEEN 8 AND 9');
        })->get();
        
        foreach ($vendorsNearingLimit as $vendor) {
            // Send reminder notification
            // This could be email, SMS, or in-app notification
        }
        
        // Get vendors with expiring subscriptions (within 3 days)
        $expiringSoon = VendorSubscription::where('status', 'active')
            ->where('ends_at', '<=', now()->addDays(3))
            ->where('ends_at', '>', now())
            ->with('vendor')
            ->get();
            
        foreach ($expiringSoon as $subscription) {
            // Send renewal reminder
        }
    }
}
