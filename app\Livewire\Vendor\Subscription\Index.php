<?php

namespace App\Livewire\Vendor\Subscription;

use App\Models\SubscriptionPlan;
use App\Models\VendorSubscription;
use App\Services\PaystackService;
use App\Services\SubscriptionService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Livewire\Attributes\Layout;
use Livewire\Component;

#[Layout('layouts.vendor')]
class Index extends Component
{
    public $subscription;
    public $subscriptionStatus;
    public $standardPlan;
    protected $paystackService;
    protected $subscriptionService;

    public function boot(PaystackService $paystackService, SubscriptionService $subscriptionService)
    {
        $this->paystackService = $paystackService;
        $this->subscriptionService = $subscriptionService;
    }

    public function mount()
    {
        $vendor = Auth::user()->vendor;
        $this->subscription = $vendor->subscription;
        $this->subscriptionStatus = $this->subscriptionService->getSubscriptionStatus($vendor);
        $this->standardPlan = $this->subscriptionService->ensureStandardPlan();
    }

    public function subscribeToStandard()
    {
        $vendor = Auth::user()->vendor;
        $user = Auth::user();

        // Check if vendor has exceeded free tier
        $orderCount = $vendor->orders()->count();
        if ($orderCount < 10) {
            session()->flash('info', 'You still have ' . (10 - $orderCount) . ' free orders remaining. You can subscribe anytime.');
            return;
        }

        // Initialize Paystack transaction for standard plan
        $reference = 'sub_' . Str::random(10);

        $data = [
            'email' => $user->email,
            'amount' => $this->standardPlan->price * 100, // Paystack amount is in kobo
            'reference' => $reference,
            'callback_url' => route('vendor.subscription.callback'),
            'metadata' => [
                'plan_id' => $this->standardPlan->id,
                'vendor_id' => $vendor->id,
                'user_id' => $user->id,
                'subscription_type' => 'standard'
            ]
        ];

        $result = $this->paystackService->initializeTransaction($data);

        if ($result && $result['status']) {
            return $this->redirect($result['data']['authorization_url']);
        }

        session()->flash('error', $result['message'] ?? 'Could not initialize subscription. Please try again.');
    }

    public function cancel()
    {
        $vendor = Auth::user()->vendor;
        $subscription = $vendor->subscription;

        if ($subscription) {
            // This would ideally interact with Paystack's API to cancel the subscription
            // For now, we'll just mark it as cancelled locally.
            $subscription->update(['status' => 'cancelled']);
            session()->flash('success', 'Your subscription has been cancelled.');
        } else {
            session()->flash('error', 'You do not have an active subscription.');
        }
        
        return $this->redirect(route('vendor.subscription.index'), navigate: true);
    }

    public function render()
    {
        $vendor = Auth::user()->vendor;
        $orderCount = $vendor->orders()->count();

        return view('livewire.vendor.subscription.index', [
            'orderCount' => $orderCount,
        ]);
    }
}
