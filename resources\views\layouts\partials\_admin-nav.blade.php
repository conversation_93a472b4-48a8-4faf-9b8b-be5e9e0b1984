@php
$navLinks = [
    ['route' => 'admin.dashboard', 'label' => 'Dashboard', 'icon' => 'fa-chart-line', 'active' => 'admin.dashboard'],
    ['route' => 'admin.users.index', 'label' => 'Users', 'icon' => 'fa-users', 'active' => 'admin.users.*'],
    ['route' => 'admin.vendors.index', 'label' => 'Vendors', 'icon' => 'fa-store', 'active' => 'admin.vendors.*'],
    ['route' => 'admin.categories.index', 'label' => 'Categories', 'icon' => 'fa-tags', 'active' => 'admin.categories.*'],
    ['route' => 'admin.products.index', 'label' => 'Products', 'icon' => 'fa-box', 'active' => 'admin.products.*'],
    ['route' => 'admin.orders.index', 'label' => 'Orders', 'icon' => 'fa-shopping-cart', 'active' => 'admin.orders.*'],
    ['route' => 'admin.withdrawals.index', 'label' => 'Withdrawals', 'icon' => 'fa-money-bill-wave', 'active' => 'admin.withdrawals.*'],
    ['route' => 'admin.subscriptions.index', 'label' => 'Subscriptions', 'icon' => 'fa-credit-card', 'active' => 'admin.subscriptions.*'],
];
@endphp

<nav class="flex-1 space-y-2 px-4 pb-4">
    @foreach ($navLinks as $link)
        <a href="{{ route($link['route']) }}"
           class="{{ request()->routeIs($link['active'] ?? $link['route']) ? 'bg-white/10 text-white border-r-4 border-white shadow-lg' : 'text-gray-300 hover:bg-white/5 hover:text-white' }} group flex items-center rounded-xl px-4 py-3 text-sm font-medium transition-all duration-200 hover:scale-105 hover:shadow-lg">
            <i class="fas {{ $link['icon'] }} mr-4 text-lg {{ request()->routeIs($link['active'] ?? $link['route']) ? 'text-white' : 'text-gray-400 group-hover:text-gray-300' }}"></i>
            <span class="font-semibold">{{ $link['label'] }}</span>
        </a>
    @endforeach

    <div class="pt-4 space-y-1">
        <a href="{{ route('home') }}" class="text-gray-300 hover:bg-gray-700 hover:text-white group flex items-center rounded-md px-2 py-2 text-sm font-medium">
            <i class="fas fa-home mr-3 h-6 w-6 flex-shrink-0 text-gray-400 group-hover:text-gray-300"></i>
            Back to Site
        </a>
        <form method="POST" action="{{ route('logout') }}">
            @csrf
            <button type="submit" class="text-gray-300 hover:bg-gray-700 hover:text-white group flex w-full items-center rounded-md px-2 py-2 text-sm font-medium">
                <i class="fas fa-sign-out-alt mr-3 h-6 w-6 flex-shrink-0 text-gray-400 group-hover:text-gray-300"></i>
                Logout
            </button>
        </form>
    </div>
</nav>
