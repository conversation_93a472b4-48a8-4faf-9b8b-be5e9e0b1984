<div>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                <!-- Filters Sidebar -->
                <aside class="lg:col-span-1">
                    <div class="p-4 bg-white dark:bg-gray-800 shadow-sm sm:rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Filters</h3>

                        <!-- Search Filter -->
                        <div class="mb-6">
                            <input wire:model.live.debounce.300ms="search" type="text" placeholder="Search products..." class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white">
                        </div>

                        <!-- Category Filter -->
                        <div class="mb-6">
                            <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-2">Category</h4>
                            <select wire:model.live="category" class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <option value="">All Categories</option>
                                @foreach ($categories as $cat)
                                    <option value="{{ $cat->slug }}">{{ $cat->name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Brand Filter -->
                        <div class="mb-6">
                            <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-2">Brand</h4>
                            <select wire:model.live="brand" class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <option value="">All Brands</option>
                                @foreach ($brands as $br)
                                    <option value="{{ $br->slug }}">{{ $br->name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Price Range Filter -->
                        <div>
                            <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-2">Price Range</h4>
                            <div class="flex space-x-2">
                                <input wire:model.live.debounce.300ms="min_price" type="number" placeholder="Min" class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white">
                                <input wire:model.live.debounce.300ms="max_price" type="number" placeholder="Max" class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white">
                            </div>
                        </div>
                    </div>
                </aside>

                <!-- Products Grid -->
                <main class="lg:col-span-3">
                    <!-- Sorting -->
                    <div class="flex justify-between items-center mb-4">
                        <p class="text-sm text-gray-700 dark:text-gray-300">
                            Showing <span class="font-medium">{{ $products->firstItem() }}</span> to <span class="font-medium">{{ $products->lastItem() }}</span> of <span class="font-medium">{{ $products->total() }}</span> results
                        </p>
                        <select wire:model.live="sort_by" class="w-48 px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="latest">Sort by latest</option>
                            <option value="price_asc">Sort by price: low to high</option>
                            <option value="price_desc">Sort by price: high to low</option>
                            <option value="name_asc">Sort by name: A to Z</option>
                            <option value="name_desc">Sort by name: Z to A</option>
                        </select>
                    </div>

                    <div wire:loading.class="opacity-50" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                        @forelse ($products as $product)
                            <livewire:product-card :product="$product" :key="$product->id" />
                        @empty
                            <div class="sm:col-span-2 md:col-span-3 text-center py-12">
                                <p class="text-lg text-gray-500">No products found matching your criteria.</p>
                            </div>
                        @endforelse
                    </div>

                    <!-- Pagination -->
                    <div class="mt-8">
                        {{ $products->links() }}
                    </div>
                </main>
            </div>
        </div>
    </div>
</div>
