<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    {{-- Modern Header --}}
    <div class="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-2xl shadow-2xl mb-8">
        <div class="flex items-center justify-between">
            <div class="space-y-2">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                    Subscription Management
                </h1>
                <p class="text-gray-300 text-lg">Manage your subscription and billing</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('vendor.dashboard') }}"
                   class="group border-2 border-white text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-white hover:text-black hover:scale-105 flex items-center space-x-2">
                    <i class="fa-solid fa-arrow-left transition-transform duration-300 group-hover:-translate-x-1"></i>
                    <span>Back to Dashboard</span>
                </a>
            </div>
        </div>
    </div>

    {{-- Flash Messages --}}
    @if (session()->has('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-xl mb-6 flex items-center">
            <i class="fas fa-check-circle mr-3"></i>
            {{ session('success') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-xl mb-6 flex items-center">
            <i class="fas fa-exclamation-circle mr-3"></i>
            {{ session('error') }}
        </div>
    @endif

    @if (session()->has('info'))
        <div class="bg-blue-100 border border-blue-400 text-blue-700 px-6 py-4 rounded-xl mb-6 flex items-center">
            <i class="fas fa-info-circle mr-3"></i>
            {{ session('info') }}
        </div>
    @endif

    {{-- Subscription Status Card --}}
    <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mb-8">
        <div class="p-8">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold text-gray-900">Current Status</h2>
                <div class="px-4 py-2 rounded-full text-sm font-semibold
                    @if($subscriptionStatus['status'] === 'free_trial') bg-blue-100 text-blue-800
                    @elseif($subscriptionStatus['status'] === 'active') bg-green-100 text-green-800
                    @else bg-red-100 text-red-800 @endif">
                    @if($subscriptionStatus['status'] === 'free_trial') Free Trial
                    @elseif($subscriptionStatus['status'] === 'active') Active Subscription
                    @elseif($subscriptionStatus['status'] === 'subscription_required') Subscription Required
                    @elseif($subscriptionStatus['status'] === 'subscription_expired') Subscription Expired
                    @endif
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                {{-- Orders Count --}}
                <div class="text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl">
                    <div class="text-3xl font-bold text-blue-600 mb-2">{{ $orderCount }}</div>
                    <div class="text-sm text-gray-600">Total Orders</div>
                    @if($orderCount < 10)
                        <div class="text-xs text-blue-600 mt-1">{{ 10 - $orderCount }} free orders remaining</div>
                    @endif
                </div>

                {{-- Subscription Status --}}
                <div class="text-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl">
                    @if($subscription && $subscription->status === 'active')
                        <div class="text-3xl font-bold text-green-600 mb-2">₦{{ number_format($standardPlan->price) }}</div>
                        <div class="text-sm text-gray-600">Monthly Plan</div>
                        <div class="text-xs text-green-600 mt-1">{{ $subscriptionStatus['days_remaining'] ?? 0 }} days remaining</div>
                    @else
                        <div class="text-3xl font-bold text-gray-400 mb-2">—</div>
                        <div class="text-sm text-gray-600">No Active Plan</div>
                    @endif
                </div>

                {{-- Next Action --}}
                <div class="text-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl">
                    @if($subscriptionStatus['can_receive_orders'])
                        <div class="text-3xl font-bold text-green-600 mb-2">✓</div>
                        <div class="text-sm text-gray-600">Can Receive Orders</div>
                    @else
                        <div class="text-3xl font-bold text-red-600 mb-2">✗</div>
                        <div class="text-sm text-gray-600">Orders Blocked</div>
                    @endif
                </div>
            </div>

            <div class="mt-6 p-4 bg-gray-50 rounded-xl">
                <p class="text-gray-700 text-center">{{ $subscriptionStatus['message'] }}</p>
            </div>

            @if($subscription && $subscription->status === 'active')
                <div class="mt-6 text-center">
                    <button wire:click="cancel"
                            wire:confirm="Are you sure you want to cancel your subscription? This action cannot be undone."
                            class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-xl transition duration-300 transform hover:scale-105">
                        Cancel Subscription
                    </button>
                </div>
            @endif
        </div>
    </div>

    {{-- Subscription Plan Card --}}
    <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
        <div class="p-8">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Standard Plan</h2>
                <p class="text-gray-600">Perfect for growing businesses</p>
            </div>

            <div class="text-center mb-8">
                <div class="text-6xl font-bold text-black mb-2">₦{{ number_format($standardPlan->price) }}</div>
                <div class="text-gray-600 text-lg">per month</div>
                <div class="text-sm text-gray-500 mt-2">First 10 orders are FREE!</div>
            </div>

            {{-- Features --}}
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">What's included:</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @foreach(json_decode($standardPlan->features, true) as $feature)
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check text-green-600 text-sm"></i>
                                </div>
                            </div>
                            <span class="text-gray-700">{{ $feature }}</span>
                        </div>
                    @endforeach
                </div>
            </div>

            {{-- Action Button --}}
            <div class="text-center">
                @if($subscriptionStatus['status'] === 'free_trial')
                    <button wire:click="subscribeToStandard"
                            wire:loading.attr="disabled"
                            class="bg-gradient-to-r from-black to-gray-800 hover:from-gray-800 hover:to-black text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        <span wire:loading.remove wire:target="subscribeToStandard">
                            <i class="fas fa-rocket mr-2"></i>
                            Subscribe Now
                        </span>
                        <span wire:loading wire:target="subscribeToStandard">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            Processing...
                        </span>
                    </button>
                    <p class="text-sm text-gray-500 mt-3">You can subscribe anytime during or after your free trial</p>
                @elseif($subscriptionStatus['status'] === 'active')
                    <div class="bg-green-100 text-green-800 py-4 px-6 rounded-xl">
                        <i class="fas fa-check-circle mr-2"></i>
                        You're subscribed to this plan
                    </div>
                @else
                    <button wire:click="subscribeToStandard"
                            wire:loading.attr="disabled"
                            class="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        <span wire:loading.remove wire:target="subscribeToStandard">
                            <i class="fas fa-unlock mr-2"></i>
                            Subscribe to Continue
                        </span>
                        <span wire:loading wire:target="subscribeToStandard">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            Processing...
                        </span>
                    </button>
                    <p class="text-sm text-red-600 mt-3">Subscribe now to continue receiving orders</p>
                @endif
            </div>
        </div>
    </div>
</div>
