<div id="reviews">
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            <button wire:click="selectTab('description')" class="<?php echo e($activeTab === 'description' ? 'border-black text-black' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                Description
            </button>
            <button wire:click="selectTab('specs')" class="<?php echo e($activeTab === 'specs' ? 'border-black text-black' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                Specifications
            </button>
            <button wire:click="selectTab('reviews')" class="<?php echo e($activeTab === 'reviews' ? 'border-black text-black' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'); ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                Reviews (<?php echo e($product->reviewCount()); ?>)
            </button>
        </nav>
    </div>

    <div class="mt-6">
        <div x-data="{ activeTab: <?php if ((object) ('activeTab') instanceof \Livewire\WireDirective) : ?>window.Livewire.find('<?php echo e($__livewire->getId()); ?>').entangle('<?php echo e('activeTab'->value()); ?>')<?php echo e('activeTab'->hasModifier('live') ? '.live' : ''); ?><?php else : ?>window.Livewire.find('<?php echo e($__livewire->getId()); ?>').entangle('<?php echo e('activeTab'); ?>')<?php endif; ?> }">
            <div x-show="activeTab === 'description'" class="prose prose-sm max-w-none text-gray-500">
                <?php echo $product->description; ?>

            </div>

            <div x-show="activeTab === 'specs'" class="prose prose-sm max-w-none text-gray-500">
                
                <p>No specifications available for this product.</p>
            </div>

            <div x-show="activeTab === 'reviews'">
                
                <div class="space-y-10">
                    <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $reviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="flex flex-col sm:flex-row">
                            <div class="mt-6 sm:mt-0 sm:ml-4 md:ml-6">
                                <h3 class="text-sm font-medium text-black"><?php echo e($review->user->name); ?></h3>
                                <p><time datetime="<?php echo e($review->created_at->toIso8601String()); ?>" class="text-sm text-gray-500"><?php echo e($review->created_at->format('F j, Y')); ?></time></p>
                                <div class="mt-1 flex items-center">
                                    <!--[if BLOCK]><![endif]--><?php for($i = 1; $i <= 5; $i++): ?>
                                        <svg class="h-5 w-5 flex-shrink-0 <?php echo e($i <= $review->rating ? 'text-yellow-400' : 'text-gray-300'); ?>" viewBox="0 0 20 20" fill="currentColor"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" /></svg>
                                    <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                                <div class="mt-4 prose prose-sm max-w-none text-gray-500">
                                    <p><?php echo e($review->comment); ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <p class="text-sm text-gray-500">No reviews yet. Be the first to share your thoughts!</p>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                <div class="mt-6">
                    <?php echo e($reviews->links()); ?>

                </div>

                
                <div class="mt-10">
                    <h3 class="text-lg font-medium text-black">Write a review</h3>
                    <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->check()): ?>
                        <form wire:submit.prevent="submitReview" class="mt-4 space-y-6">
                            <div>
                                <label for="rating" class="text-sm font-medium text-black">Rating</label>
                                <div class="mt-1 flex items-center">
                                    <!--[if BLOCK]><![endif]--><?php for($i = 1; $i <= 5; $i++): ?>
                                        <button type="button" wire:click="$set('rating', <?php echo e($i); ?>)">
                                            <svg class="h-6 w-6 <?php echo e($rating >= $i ? 'text-yellow-400' : 'text-gray-300'); ?>" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" /></svg>
                                        </button>
                                    <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['rating'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-sm text-red-500"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                            <div>
                                <label for="comment" class="block text-sm font-medium text-black">Your Review</label>
                                <div class="mt-1">
                                    <textarea wire:model.defer="comment" id="comment" rows="4" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black sm:text-sm"></textarea>
                                </div>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['comment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-sm text-red-500"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                            <div>
                                <button type="submit" class="inline-flex items-center justify-center rounded-md border border-transparent bg-black px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2">
                                    <span wire:loading.remove wire:target="submitReview">Submit Review</span>
                                    <span wire:loading wire:target="submitReview">Submitting...</span>
                                </button>
                            </div>
                        </form>
                    <?php else: ?>
                        <p class="mt-4 text-sm text-gray-500">Please <a href="<?php echo e(route('login')); ?>" class="font-medium text-black hover:underline">log in</a> to write a review.</p>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Music\brandifyng\brandifyng\resources\views/livewire/product/tabs.blade.php ENDPATH**/ ?>