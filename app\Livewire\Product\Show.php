<?php

namespace App\Livewire\Product;

use App\Models\Product;
use App\Services\ViewedProductsService;
use Livewire\Component;

class Show extends Component
{
    public Product $product;
    public $relatedProducts;
    public $recentlyViewedProducts;

    public function mount(Product $product, ViewedProductsService $viewedProductsService)
    {
        $this->product = $product;

        // Track this product view
        $viewedProductsService->addProduct($this->product);

        // Load related products
        $this->relatedProducts = Product::query()
            ->where('category_id', $this->product->category_id)
            ->where('id', '!=', $this->product->id)
            ->where('is_active', true)
            ->limit(4)
            ->get();

        // Get recently viewed products
        $this->recentlyViewedProducts = $viewedProductsService->getRecentlyViewedProducts(5)
            ->filter(fn($item) => $item->id !== $this->product->id)
            ->take(4);
    }

    public function render()
    {
        return view('livewire.product.show');
    }
}
